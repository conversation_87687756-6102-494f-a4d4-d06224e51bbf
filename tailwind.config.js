/** @type {import('tailwindcss').Config} */


import { fontFamily } from 'tailwindcss/defaultTheme'

module.exports={

 content : [
  // Or if using `src` directory:
  "./src/**/*.{js,ts,jsx,tsx}",
],
darkMode:'class',
  theme : {
  extend: {
    fontFamily: {
    },
    colors:{
      dark: "#1b1b1b",
      light: "#f5f5f5",
      primary: "#B63E96", // 240,86,199
      primaryDark: "#58E6D9", // 80,230,217
    },
    animation:{
      'spin-slow':'spin 8s linear infinite',
      'pulse': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      'ping': 'ping 2s cubic-bezier(0, 0, 0.2, 1) infinite'
    },
    animationDelay: {
      '2000': '2000ms',
    },
    backgroundImage:{
      circularLight:' repeating-radial-gradient(rgba(0,0,0,0.4) 2px, #f5f5f5 5px ,#f5f5f5 100px);',
      circularDark:' repeating-radial-gradient(rgba(234,156,190,0.9) 2px, #1b1b1b  5px ,#1b1b1b 100px);',

      circularLightLg:' repeating-radial-gradient(rgba(0,0,0,0.4) 2px, #f5f5f5 5px ,#f5f5f5 80px);',
      circularDarkLg:' repeating-radial-gradient(rgba(234,156,190,0.9) 2px, #1b1b1b  5px ,#1b1b1b 80px);',

      circularLightMd:' repeating-radial-gradient(rgba(0,0,0,0.4) 2px, #f5f5f5 5px ,#f5f5f5 60px);',
      circularDarkMd:' repeating-radial-gradient(rgba(234,156,190,0.9) 2px, #1b1b1b  5px ,#1b1b1b 60px);',

      circularLightSm:' repeating-radial-gradient(rgba(0,0,0,0.4) 2px, #f5f5f5 5px ,#f5f5f5 40px);',
      circularDarkSm:' repeating-radial-gradient(rgba(234,156,190,0.9) 2px, #1b1b1b  5px ,#1b1b1b 40px);'
    },
    boxShadow: {
      'custom': '0 10px 30px -10px rgba(0, 0, 0, 0.3)',
      'custom-dark': '0 10px 30px -10px rgba(255, 255, 255, 0.1)'
    }
  },

screens: {
  "2xl": { max: "1535px" },
  // => @media (max-width: 1535px) { ... }

  xl: { max: "1279px" },
  // => @media (max-width: 1279px) { ... }

  lg: { max: "1023px" },
  // => @media (max-width: 1023px) { ... }

  md: { max: "767px" },
  // => @media (max-width: 767px) { ... }

  sm: { max: "639px" },
  // => @media (max-width: 639px) { ... }

  xs: { max: "479px" },
  // => @media (max-width: 479px) { ... }
},
}
}
export const plugins = []

