import React, { useRef } from "react";
import { motion, useScroll } from "framer-motion";
import LiIcon from "./LiIcon";

const Details = ({ position, company, companyLink, time, address, work }) => {
  const ref = useRef(null);

  return (
    <li ref={ref} className="my-8 first:mt-0 last:mb-0 w-[60%] mx-auto flex flex-col items-start justify-between md:w-[80%] xs:w-[85%]">
      <LiIcon reference={ref} />
      <motion.div
        initial={{ y: 50 }}
        whileInView={{ y: 0 }}
        transition={{ duration: 0.5, type: "spring" }}
        className="flex flex-col items-start justify-start ml-4 sm:ml-6 md:ml-4"
      >
        <h3 className="capitalize font-bold text-2xl text-dark dark:text-light sm:text-xl xs:text-lg">
          {position} &nbsp;
          {companyLink ? (
            <a
              href={companyLink}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary dark:text-primaryDark capitalize hover:underline"
            >
              @{company}
            </a>
          ) : (
            <span className="text-primary dark:text-primaryDark capitalize">@{company}</span>
          )}
        </h3>
        <span className="capitalize font-medium text-dark/75 dark:text-light/75 xs:text-sm">
          {time} {address && `| ${address}`}
        </span>
        <p className="font-medium w-full text-dark dark:text-light md:text-sm">
          {work}
        </p>
      </motion.div>
    </li>
  );
};

const Experience = () => {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "center start"]
  });

  return (
    <div className="my-64">
      <h2 className="font-bold text-8xl mb-32 w-full text-center text-dark dark:text-light lg:!text-7xl sm:!text-6xl xs:!text-4xl">
        Experience
      </h2>

      <div ref={ref} className="w-[75%] mx-auto relative lg:w-[90%] md:w-full">
        <motion.div
          style={{ scaleY: scrollYProgress }}
          className="absolute left-9 top-0 w-[4px] h-full bg-dark dark:bg-light origin-top md:w-[2px] md:left-[30px] xs:left-[10px]"
        />

        <ul className="w-full flex flex-col items-start justify-between ml-4 xs:ml-6">
          <Details
            position="Frontend Developer"
            company="Zetpeak"
            companyLink="https://zetpeak.com"
            time="June 2023 - September 2023"
            address="Remote"
            work="Worked on a team responsible for developing new features for Zetpeak's client projects, including optimizing website performance and improving user experience."
          />

          <Details
            position="Web Developer Intern"
            company="TechVritti"
            companyLink="https://techvritti.com"
            time="March 2025 - Present"
            address="Bengalore India"
            work="Assisted in developing and maintaining client websites, learned modern web development practices, and collaborated with senior developers on various projects."
          />

          <Details
            position="Freelance Developer"
            company="Self-employed"
            time="2022 - Present"
            work="Designed and developed websites for small businesses and individuals. Implemented responsive designs and modern UI/UX practices to create engaging user experiences."
          />
        </ul>
      </div>
    </div>
  );
};

export default Experience;
