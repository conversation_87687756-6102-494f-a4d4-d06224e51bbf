@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom utilities */
.clip-path-triangle {
  clip-path: polygon(0 0, 100% 0, 100% 100%);
}

/* Image effects */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

/* Gradient animations */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 15s ease infinite;
}

/* Pulse animation */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 0.4;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 3s ease-in-out infinite;
}

/* Animation delay utilities */
.animation-delay-2000 {
  animation-delay: 2000ms;
}

/* Hover effects */
@keyframes float-up {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}

.hover-float:hover {
  animation: float-up 1s ease-in-out;
}