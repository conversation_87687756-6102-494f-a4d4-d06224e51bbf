{"name": "developer-portfolio-starter-code", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"eslint": "8.36.0", "eslint-config-next": "13.2.4", "framer-motion": "^10.12.4", "next": "13.2.4", "react": "18.2.0", "react-dom": "18.2.0", "rss-parser": "^3.13.0"}, "devDependencies": {"autoprefixer": "^10.4.14", "postcss": "^8.4.31", "tailwindcss": "^3.3.1"}, "description": "![GitHub stars](https://img.shields.io/github/stars/codebucks27/Next.js-Developer-Portfolio-Starter-Code?style=social&logo=ApacheSpark&label=Stars)&nbsp;&nbsp;\r ![GitHub forks](https://img.shields.io/github/forks/codebucks27/Next.js-Developer-Portfolio-Starter-Code?style=social&logo=KashFlow&maxAge=3600)&nbsp;&nbsp;\r ![Github Followers](https://img.shields.io/github/followers/codebucks27.svg?style=social&label=Follow)&nbsp;&nbsp;<br />", "main": "next.config.js", "repository": {"type": "git", "url": "git+https://github.com/codebucks27/Next.js-Developer-Portfolio-Starter-Code.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/codebucks27/Next.js-Developer-Portfolio-Starter-Code/issues"}, "homepage": "https://github.com/codebucks27/Next.js-Developer-Portfolio-Starter-Code#readme"}