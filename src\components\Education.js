import React, { useRef } from "react";
import { motion, useScroll } from "framer-motion";
import LiIcon from "./LiIcon";

const Details = ({ type, time, place, info }) => {
  const ref = useRef(null);

  return (
    <li ref={ref} className="my-8 first:mt-0 last:mb-0 w-[60%] mx-auto flex flex-col items-start justify-between md:w-[80%] xs:w-[85%]">
      <LiIcon reference={ref} />
      <motion.div
        initial={{ y: 50 }}
        whileInView={{ y: 0 }}
        transition={{ duration: 0.5, type: "spring" }}
        className="flex flex-col items-start justify-start ml-4 sm:ml-6 md:ml-4"
      >
        <h3 className="capitalize font-bold text-2xl text-dark dark:text-light sm:text-xl xs:text-lg">
          {type}
        </h3>
        <span className="capitalize font-medium text-dark/75 dark:text-light/75 xs:text-sm">
          {time} | {place}
        </span>
        {info && (
          <p className="font-medium w-full text-dark dark:text-light md:text-sm">
            {info}
          </p>
        )}
      </motion.div>
    </li>
  );
};

const Education = () => {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "center start"]
  });

  return (
    <div className="my-64">
      <h2 className="font-bold text-8xl mb-32 w-full text-center text-dark dark:text-light lg:!text-7xl sm:!text-6xl xs:!text-4xl">
        Education
      </h2>

      <div ref={ref} className="w-[75%] mx-auto relative lg:w-[90%] md:w-full">
        <motion.div
          style={{ scaleY: scrollYProgress }}
          className="absolute left-9 top-0 w-[4px] h-full bg-dark dark:bg-light origin-top md:w-[2px] md:left-[30px] xs:left-[10px]"
        />

        <ul className="w-full flex flex-col items-start justify-between ml-4 xs:ml-6">
          <Details
            type="Bachelor of Technology in Computer Science"
            time="2021-2025"
            place="NIST UNIVERSITY"
            info="Relevant coursework included Data Structures, Algorithms, Computer Systems, and Artificial Intelligence."
          />

          <Details
            type="10 th (Matriculation)"
            time="2017 - 2018"
            place="Garh Haripur G.N High Scool"
          />

          <Details
            type="12th (High School)"
            time="2019 - 2020"
            place="Garh Haripur G.N High Scool"
          />
        </ul>
      </div>
    </div>
  );
};

export default Education;
